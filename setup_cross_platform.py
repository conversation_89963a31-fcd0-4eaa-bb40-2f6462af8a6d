#!/usr/bin/env python3
"""
Kitco Research AI - Cross-Platform Setup Convenience Script
This script redirects to the organized cross-platform setup utility
"""

import sys
import subprocess
from pathlib import Path


def main():
    """Main entry point - redirect to organized script"""
    # Find project root
    current_dir = Path.cwd()
    
    # Check if we're in the right directory
    if not (current_dir / 'app.py').exists() or not (current_dir / 'src' / 'kitco_research_ai').exists():
        print("❌ Error: This script must be run from the project root directory")
        print("   Please navigate to the kitco_research_ai directory first")
        return 1
    
    # Run the organized cross-platform utility
    organized_script = current_dir / 'scripts' / 'setup' / 'setup_cross_platform.py'
    
    if not organized_script.exists():
        print(f"❌ Error: Organized script not found: {organized_script}")
        return 1
    
    print("🔄 Redirecting to organized cross-platform setup utility...")
    print(f"📁 Script: {organized_script}")
    print()
    
    # Pass all arguments to the organized script
    cmd = [sys.executable, str(organized_script)] + sys.argv[1:]
    
    try:
        result = subprocess.run(cmd)
        return result.returncode
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
        return 1
    except Exception as e:
        print(f"❌ Error running organized script: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
